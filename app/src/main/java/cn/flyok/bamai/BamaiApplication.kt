package cn.flyok.bamai

import android.app.Application
import coil.ImageLoader
import coil.ImageLoaderFactory
import coil.decode.GifDecoder
import coil.decode.ImageDecoderDecoder
import coil.util.DebugLogger
import cn.flyok.bamai.data.initializeHomeDataManager

/**
 * 把脉App的Application类
 * 用于初始化全局配置，包括Coil的GIF支持
 */
class BamaiApplication : Application(), ImageLoaderFactory {
    
    override fun onCreate() {
        super.onCreate()

        // 初始化全局数据管理器
        initializeHomeDataManager(this)
    }
    
    /**
     * 创建自定义的ImageLoader，支持GIF动画
     */
    override fun newImageLoader(): ImageLoader {
        return ImageLoader.Builder(this)
            .components {
                // 添加GIF解码器支持
                if (android.os.Build.VERSION.SDK_INT >= 28) {
                    add(ImageDecoderDecoder.Factory())
                } else {
                    add(GifDecoder.Factory())
                }
            }
            .logger(DebugLogger()) // 添加调试日志
            .build()
    }
}
