package cn.flyok.bamai.ui.constitution

import androidx.compose.foundation.Image
import androidx.compose.foundation.background
import androidx.compose.foundation.border
import androidx.compose.foundation.clickable
import androidx.compose.foundation.interaction.MutableInteractionSource
import androidx.compose.foundation.layout.*
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.material3.*
import androidx.compose.runtime.*
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.blur
import androidx.compose.ui.draw.clip
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.layout.ContentScale
import androidx.compose.ui.res.painterResource
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.text.style.TextAlign
import androidx.compose.ui.tooling.preview.Preview
import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.sp
import cn.flyok.bamai.R
import cn.flyok.bamai.ui.theme.*

/**
 * 关于我的体质卡片组件 - 完全复刻iOS端ConAboutTableViewCell
 * 
 * 特点：
 * - 显示体质定义和形成原因
 * - 非会员显示锁定状态
 */
@Composable
fun AboutConstitutionCard(
    constitutionData: ConstitutionData,
    modifier: Modifier = Modifier
) {
    Card(
        modifier = modifier.fillMaxWidth(),
        shape = RoundedCornerShape(20.dp),
        colors = CardDefaults.cardColors(containerColor = Color(0xFFFFFDFA)),
        elevation = CardDefaults.cardElevation(defaultElevation = 0.dp)
    ) {
        Box(
            modifier = Modifier
                .fillMaxWidth()
                .padding(5.dp)
        ) {
            Card(
                modifier = Modifier.fillMaxWidth(),
                shape = RoundedCornerShape(20.dp),
                colors = CardDefaults.cardColors(containerColor = Color(0xFFFFFDFA)),
                elevation = CardDefaults.cardElevation(defaultElevation = 0.dp)
            ) {
                Box(
                    modifier = Modifier
                        .fillMaxWidth()
                        .border(
                            width = 1.dp,
                            color = Color(0xFFF0E9E1),
                            shape = RoundedCornerShape(20.dp)
                        )
                        .padding(1.dp)
                ) {
                    Column(
                        modifier = Modifier
                            .fillMaxWidth()
                            .padding(22.dp)
                    ) {
                        // 标题行
                        Row(
                            verticalAlignment = Alignment.CenterVertically
                        ) {
                            Image(
                                painter = painterResource(id = R.drawable.con_xqtj),
                                contentDescription = null,
                                modifier = Modifier.size(18.dp)
                            )
                            
                            Spacer(modifier = Modifier.width(5.dp))
                            
                            Text(
                                text = "关于我的体质",
                                style = MaterialTheme.typography.titleMedium.copy(
                                    fontWeight = FontWeight.Bold,
                                    fontSize = 16.sp
                                ),
                                color = Color(0xFF333333)
                            )
                        }
                        
                        Spacer(modifier = Modifier.height(20.dp))
                        
                        // 内容区域
                        if (constitutionData.isVip && constitutionData.hasEnoughData) {
                            VipAboutContent(constitutionData = constitutionData)
                        } else {
                            LockedAboutContent()
                        }
                    }
                }
            }
        }
    }
}

/**
 * 会员关于体质内容
 */
@Composable
private fun VipAboutContent(
    constitutionData: ConstitutionData
) {
    Column {
        // 什么是XX体质
        if (constitutionData.definition.isNotEmpty()) {
            AboutSection(
                title = "什么是${constitutionData.mainName}",
                content = constitutionData.definition
            )
            
            Spacer(modifier = Modifier.height(20.dp))
        }
        
        // 形成原因
        if (constitutionData.causes.isNotEmpty()) {
            AboutSection(
                title = "形成原因",
                content = constitutionData.causes
            )
        }
    }
}

/**
 * 关于体质区块
 */
@Composable
private fun AboutSection(
    title: String,
    content: String
) {
    Column {
        Text(
            text = title,
            style = MaterialTheme.typography.titleSmall.copy(
                fontWeight = FontWeight.Bold,
                fontSize = 14.sp
            ),
            color = Color(0xFF333333)
        )
        
        Spacer(modifier = Modifier.height(8.dp))
        
        Text(
            text = content,
            style = MaterialTheme.typography.bodyMedium.copy(
                fontSize = 14.sp,
                lineHeight = 19.sp
            ),
            color = Color(0xFF333333)
        )
    }
}

/**
 * 锁定状态关于体质内容
 */
@Composable
private fun LockedAboutContent() {
    Box(
        modifier = Modifier
            .fillMaxWidth()
            .clickable(
                onClick = { /* TODO: 跳转订阅页面 */ },
                indication = null,
                interactionSource = remember { MutableInteractionSource() }
            )
    ) {
        // 显示模糊的实际内容
        Column(
            modifier = Modifier
                .fillMaxWidth()
                .blur(radius = 8.dp)
        ) {
            AboutSection(
                title = "什么是平和质",
                content = "平和质是指阴阳气血调和，以体态适中、面色红润、精力充沛、脏腑功能状态强健壮实为主要特征的体质状态。"
            )

            Spacer(modifier = Modifier.height(20.dp))

            AboutSection(
                title = "形成原因",
                content = "先天禀赋良好，后天调养得当。平和质的形成主要与遗传因素、生活环境、饮食习惯等多方面因素有关。"
            )
        }

        // 锁定遮罩
        Box(
            modifier = Modifier
                .fillMaxWidth()
                .height(120.dp)
                .background(
                    Color.White.copy(alpha = 0.8f),
                    RoundedCornerShape(15.dp)
                ),
            contentAlignment = Alignment.Center
        ) {
            Column(
                horizontalAlignment = Alignment.CenterHorizontally
            ) {
                Image(
                    painter = painterResource(id = R.drawable.con_lock),
                    contentDescription = null,
                    modifier = Modifier.size(32.dp)
                )

                Spacer(modifier = Modifier.height(8.dp))

                Text(
                    text = "开通会员解锁",
                    style = MaterialTheme.typography.bodyMedium.copy(
                        fontSize = 14.sp,
                        fontWeight = FontWeight.Medium
                    ),
                    color = Color(0xFF666666)
                )
            }
        }
    }
}

@Preview(showBackground = true)
@Composable
fun AboutConstitutionCardVipPreview() {
    BamaiTheme {
        AboutConstitutionCard(
            constitutionData = ConstitutionData.mock().copy(isVip = true),
            modifier = Modifier.padding(20.dp)
        )
    }
}

@Preview(showBackground = true)
@Composable
fun AboutConstitutionCardLockedPreview() {
    BamaiTheme {
        AboutConstitutionCard(
            constitutionData = ConstitutionData.mock().copy(isVip = false),
            modifier = Modifier.padding(20.dp)
        )
    }
}
