package cn.flyok.bamai.ui.constitution

import androidx.compose.foundation.Image
import androidx.compose.foundation.background
import androidx.compose.foundation.border
import androidx.compose.foundation.clickable
import androidx.compose.foundation.interaction.MutableInteractionSource
import androidx.compose.foundation.layout.*
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.material3.*
import androidx.compose.runtime.*
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.blur
import androidx.compose.ui.draw.clip
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.layout.ContentScale
import androidx.compose.ui.res.painterResource
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.text.style.LineHeightStyle
import androidx.compose.ui.text.style.TextAlign
import androidx.compose.ui.tooling.preview.Preview
import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.sp
import cn.flyok.bamai.R
import cn.flyok.bamai.ui.theme.*


/**
 * 体质信息卡片组件 - 完全复刻iOS端ConInfoTableViewCell
 * 
 * 特点：
 * - 白色卡片背景，带边框
 * - 显示主体质和兼夹体质及百分比
 * - 体质概括描述
 * - 体质特点标签流式布局
 * - 非会员显示锁定状态
 */
@Composable
fun ConstitutionInfoCard(
    constitutionData: ConstitutionData,
    modifier: Modifier = Modifier
) {
    Card(
        modifier = modifier.fillMaxWidth(),
        shape = RoundedCornerShape(20.dp),
        colors = CardDefaults.cardColors(containerColor = Color(0xFFFFFDFA)),
        elevation = CardDefaults.cardElevation(defaultElevation = 0.dp)
    ) {
        Box(
            modifier = Modifier
                .fillMaxWidth()
                .padding(5.dp)
        ) {
            Card(
                modifier = Modifier.fillMaxWidth(),
                shape = RoundedCornerShape(20.dp),
                colors = CardDefaults.cardColors(containerColor = Color(0xFFFFFDFA)),
                elevation = CardDefaults.cardElevation(defaultElevation = 0.dp)
            ) {
                Box(
                    modifier = Modifier
                        .fillMaxWidth()
                        .border(
                            width = 1.dp,
                            color = Color(0xFFF0E9E1),
                            shape = RoundedCornerShape(20.dp)
                        )
                        .padding(1.dp)
                ) {
                    if (constitutionData.isVip && constitutionData.hasEnoughData) {
                        VipContent(constitutionData = constitutionData)
                    } else {
                        LockedContent()
                    }
                }
            }
        }
    }
}

/**
 * 会员内容显示
 */
@Composable
private fun VipContent(
    constitutionData: ConstitutionData,
    modifier: Modifier = Modifier
) {
    Column(
        modifier = modifier
            .fillMaxWidth()
            .padding(25.dp)
    ) {
        // 主体质和兼夹体质
        Row(
            modifier = Modifier.fillMaxWidth()
        ) {
            // 主体质
            Column(
                modifier = Modifier.weight(1f)
            ) {
                Text(
                    text = constitutionData.mainName.ifEmpty { "--" },
                    style = MaterialTheme.typography.titleLarge.copy(
                        fontWeight = FontWeight.Bold,
                        fontSize = 18.sp
                    ),
                    color = Color(0xFF333333)
                )
                
                Spacer(modifier = Modifier.height(8.dp))
                
                Text(
                    text = constitutionData.mainPercent.ifEmpty { "--" },
                    style = MaterialTheme.typography.bodyLarge.copy(
                        fontSize = 14.sp
                    ),
                    color = Color(0xFF333333),
                    modifier = Modifier.padding(start = 5.dp)
                )
            }
            
            // 兼夹体质
            Column(
                modifier = Modifier.weight(1f)
            ) {
                Text(
                    text = constitutionData.minorName.ifEmpty { "--" },
                    style = MaterialTheme.typography.titleLarge.copy(
                        fontWeight = FontWeight.Bold,
                        fontSize = 18.sp
                    ),
                    color = Color(0xFF333333)
                )
                
                Spacer(modifier = Modifier.height(8.dp))
                
                Text(
                    text = constitutionData.minorPercent.ifEmpty { "--" },
                    style = MaterialTheme.typography.bodyLarge.copy(
                        fontSize = 14.sp
                    ),
                    color = Color(0xFF333333),
                    modifier = Modifier.padding(start = 5.dp)
                )
            }
        }
        
        Spacer(modifier = Modifier.height(15.dp))
        
        // 体质概括
        Text(
            text = constitutionData.summary.ifEmpty { 
                "您的体质报告还未生成，请先进行三次把脉测量，开启您的健康养生之旅！" 
            },
            style = MaterialTheme.typography.bodyMedium.copy(
                fontSize = 14.sp,
                lineHeight = 19.sp
            ),
            color = Color(0xFF333333)
        )
        
        // 体质特点标签 - 使用简单的Row布局
        if (constitutionData.traits.isNotEmpty()) {
            Spacer(modifier = Modifier.height(10.dp))

            // 简化为两行显示，每行最多3个标签
            val chunkedTraits = constitutionData.traits.chunked(3)

            chunkedTraits.forEach { rowTraits ->
                Row(
                    modifier = Modifier.fillMaxWidth(),
                    horizontalArrangement = Arrangement.spacedBy(10.dp)
                ) {
                    rowTraits.forEach { trait ->
                        Card(
                            shape = RoundedCornerShape(8.dp),
                            colors = CardDefaults.cardColors(
                                containerColor = Color(0xFFF8F4F1)
                            ),
                            elevation = CardDefaults.cardElevation(defaultElevation = 0.dp)
                        ) {
                            Text(
                                text = trait,
                                style = MaterialTheme.typography.bodyMedium.copy(
                                    fontSize = 14.sp
                                ),
                                color = Color(0xFF333333),
                                modifier = Modifier.padding(horizontal = 15.dp, vertical = 8.dp),
                                textAlign = TextAlign.Center
                            )
                        }
                    }
                }

                if (rowTraits != chunkedTraits.last()) {
                    Spacer(modifier = Modifier.height(10.dp))
                }
            }
        }
    }
}

/**
 * 锁定状态内容
 */
@Composable
private fun LockedContent(
    modifier: Modifier = Modifier
) {
    Box(
        modifier = modifier
            .fillMaxWidth()
            .clickable(
                onClick = { /* TODO: 跳转订阅页面 */ },
                indication = null,
                interactionSource = remember { MutableInteractionSource() }
            )
    ) {
        // 显示模糊的实际内容
        Column(
            modifier = Modifier
                .fillMaxWidth()
                .padding(22.dp)
                .blur(radius = 8.dp)
        ) {
            // 主体质百分比
            Row(
                modifier = Modifier.fillMaxWidth(),
                horizontalArrangement = Arrangement.SpaceBetween,
                verticalAlignment = Alignment.CenterVertically
            ) {
                Text(
                    text = "平和质",
                    style = MaterialTheme.typography.titleMedium.copy(
                        fontWeight = FontWeight.Bold,
                        fontSize = 16.sp
                    ),
                    color = Color(0xFF333333)
                )

                Text(
                    text = "85%",
                    style = MaterialTheme.typography.titleMedium.copy(
                        fontWeight = FontWeight.Bold,
                        fontSize = 16.sp
                    ),
                    color = Color(0xFF333333)
                )
            }

            Spacer(modifier = Modifier.height(12.dp))

            // 兼夹体质百分比
            Row(
                modifier = Modifier.fillMaxWidth(),
                horizontalArrangement = Arrangement.SpaceBetween,
                verticalAlignment = Alignment.CenterVertically
            ) {
                Text(
                    text = "兼阴虚质",
                    style = MaterialTheme.typography.bodyMedium.copy(
                        fontSize = 14.sp
                    ),
                    color = Color(0xFF666666)
                )

                Text(
                    text = "15%",
                    style = MaterialTheme.typography.bodyMedium.copy(
                        fontSize = 14.sp
                    ),
                    color = Color(0xFF666666)
                )
            }

            Spacer(modifier = Modifier.height(16.dp))

            // 体质概括
            Text(
                text = "您的体质偏向平和，阴阳气血调和，脏腑功能正常，身体状态良好。",
                style = MaterialTheme.typography.bodyMedium.copy(
                    fontSize = 14.sp,
                    lineHeight = 19.sp
                ),
                color = Color(0xFF333333)
            )
        }

        // 锁定遮罩
        Box(
            modifier = Modifier
                .fillMaxWidth()
                .height(200.dp)
                .background(
                    Color.White.copy(alpha = 0.85f),
                    RoundedCornerShape(20.dp)
                ),
            contentAlignment = Alignment.Center
        ) {
            Column(
                horizontalAlignment = Alignment.CenterHorizontally
            ) {
                Image(
                    painter = painterResource(id = R.drawable.con_lock),
                    contentDescription = null,
                    modifier = Modifier.size(40.dp)
                )

                Spacer(modifier = Modifier.height(12.dp))

                Text(
                    text = "开通会员解锁体质详情",
                    style = MaterialTheme.typography.bodyMedium.copy(
                        fontSize = 16.sp,
                        fontWeight = FontWeight.Medium
                    ),
                    color = Color(0xFF333333)
                )

                Spacer(modifier = Modifier.height(4.dp))

                Text(
                    text = "查看完整体质分析报告",
                    style = MaterialTheme.typography.bodySmall.copy(
                        fontSize = 12.sp
                    ),
                    color = Color(0xFF999999)
                )
            }
        }
    }
}

@Preview(showBackground = true)
@Composable
fun ConstitutionInfoCardVipPreview() {
    BamaiTheme {
        ConstitutionInfoCard(
            constitutionData = ConstitutionData.mock().copy(isVip = true),
            modifier = Modifier.padding(20.dp)
        )
    }
}

@Preview(showBackground = true)
@Composable
fun ConstitutionInfoCardLockedPreview() {
    BamaiTheme {
        ConstitutionInfoCard(
            constitutionData = ConstitutionData.mock().copy(isVip = false),
            modifier = Modifier.padding(20.dp)
        )
    }
}

@Preview(showBackground = true)
@Composable
fun ConstitutionInfoCardNoDataPreview() {
    BamaiTheme {
        ConstitutionInfoCard(
            constitutionData = ConstitutionData.mock().copy(
                isVip = true,
                hasEnoughData = false,
                mainName = "",
                minorName = "",
                summary = "",
                traits = emptyList()
            ),
            modifier = Modifier.padding(20.dp)
        )
    }
}
