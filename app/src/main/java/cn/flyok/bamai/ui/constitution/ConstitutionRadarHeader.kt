package cn.flyok.bamai.ui.constitution

import androidx.compose.foundation.Canvas
import androidx.compose.foundation.background
import androidx.compose.foundation.layout.*
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.material3.*
import androidx.compose.runtime.*
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.clip
import androidx.compose.ui.geometry.Offset
import androidx.compose.ui.graphics.*
import androidx.compose.ui.graphics.drawscope.DrawScope
import androidx.compose.ui.graphics.drawscope.Stroke
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.tooling.preview.Preview
import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.sp
import cn.flyok.bamai.ui.theme.*
import kotlin.math.*

/**
 * 体质雷达图头部组件 - 完全复刻iOS端ConChatView
 * 
 * 特点：
 * - 八角雷达图显示体质分析结果
 * - 根据会员状态显示不同内容
 * - 非会员显示演示数据标签
 * - 主体质和兼夹体质显示
 */
@Composable
fun ConstitutionRadarHeader(
    constitutionData: ConstitutionData,
    modifier: Modifier = Modifier
) {
    // 计算雷达图高度，保持iOS端比例
    val radarHeight = 360.dp
    
    Column(
        modifier = modifier.fillMaxWidth(),
        horizontalAlignment = Alignment.CenterHorizontally
    ) {
        // 雷达图区域
        Box(
            modifier = Modifier
                .fillMaxWidth()
                .height(radarHeight),
            contentAlignment = Alignment.Center
        ) {
            // 雷达图
            RadarChart(
                data = if (constitutionData.hasEnoughData) {
                    if (constitutionData.isVip) {
                        constitutionData.radarData
                    } else {
                        // 非会员显示演示数据
                        listOf(70.0, 90.0, 80.0, 40.0, 35.0, 35.0, 35.0, 40.0)
                    }
                } else {
                    // 无数据显示空图
                    listOf(0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0)
                },
                labels = ConstitutionRadarLabels.labels,
                showDemo = !constitutionData.isVip && constitutionData.hasEnoughData,
                modifier = Modifier.fillMaxSize()
            )
        }
        
        Spacer(modifier = Modifier.height(10.dp))
        
        // 主体质名称
        Text(
            text = when {
                !constitutionData.hasEnoughData -> "暂无数据"
                !constitutionData.isVip -> "气虚体质"
                else -> constitutionData.mainName
            },
            style = MaterialTheme.typography.headlineMedium.copy(
                fontWeight = FontWeight.Bold,
                fontSize = 24.sp
            ),
            color = Color(0xFF333333)
        )
        
        Spacer(modifier = Modifier.height(5.dp))
        
        // 兼夹体质
        Text(
            text = when {
                !constitutionData.hasEnoughData -> "--"
                !constitutionData.isVip -> "兼阴虚质"
                else -> constitutionData.minorName
            },
            style = MaterialTheme.typography.bodyLarge.copy(
                fontSize = 14.sp
            ),
            color = Color(0xFF333333)
        )
    }
}

/**
 * 雷达图组件
 */
@Composable
private fun RadarChart(
    data: List<Double>,
    labels: List<String>,
    showDemo: Boolean,
    modifier: Modifier = Modifier
) {
    Box(modifier = modifier) {
        Canvas(
            modifier = Modifier.fillMaxSize()
        ) {
            drawRadarChart(
                data = data,
                labels = labels,
                size = size
            )
        }
        
        // 演示数据标签
        if (showDemo) {
            Card(
                modifier = Modifier
                    .align(Alignment.BottomEnd)
                    .padding(end = 0.dp, bottom = 15.dp),
                colors = CardDefaults.cardColors(
                    containerColor = Color(0xFFAE8772)
                ),
                shape = RoundedCornerShape(6.dp)
            ) {
                Text(
                    text = "演示数据",
                    style = MaterialTheme.typography.bodySmall.copy(
                        fontWeight = FontWeight.Bold,
                        fontSize = 12.sp
                    ),
                    color = Color.White,
                    modifier = Modifier.padding(horizontal = 12.dp, vertical = 6.dp)
                )
            }
        }
    }
}

/**
 * 绘制雷达图
 */
private fun DrawScope.drawRadarChart(
    data: List<Double>,
    labels: List<String>,
    size: androidx.compose.ui.geometry.Size
) {
    val center = Offset(size.width / 2, size.height / 2)
    val radius = minOf(size.width, size.height) / 2 * 0.7f
    val angleStep = 2 * PI / data.size
    
    // 绘制背景网格
    drawRadarBackground(center, radius, data.size)
    
    // 绘制数据区域
    if (data.any { it > 0 }) {
        drawRadarData(center, radius, data, angleStep)
    }
    
    // 绘制标签
    drawRadarLabels(center, radius, labels, data, angleStep)
}

/**
 * 绘制雷达图背景网格
 */
private fun DrawScope.drawRadarBackground(
    center: Offset,
    radius: Float,
    vertexCount: Int
) {
    val webColor = Color(0xFFDAD3CC)
    val evenLayerColor = Color(0xFFE5DFD4).copy(alpha = 0.5f)
    val oddLayerColor = Color(0xFFF0EDE8).copy(alpha = 0.5f)
    
    val levels = 7
    val angleStep = 2 * PI / vertexCount
    
    // 绘制同心多边形背景
    for (level in 1..levels) {
        val levelRadius = radius * level / levels
        val path = Path()
        
        for (i in 0 until vertexCount) {
            val angle = angleStep * i - PI / 2
            val x = center.x + levelRadius * cos(angle).toFloat()
            val y = center.y + levelRadius * sin(angle).toFloat()
            
            if (i == 0) {
                path.moveTo(x, y)
            } else {
                path.lineTo(x, y)
            }
        }
        path.close()
        
        // 填充背景色
        drawPath(
            path = path,
            color = if (level % 2 == 0) evenLayerColor else oddLayerColor
        )
        
        // 绘制边框
        drawPath(
            path = path,
            color = webColor,
            style = Stroke(width = 1.dp.toPx())
        )
    }
    
    // 绘制从中心到顶点的线
    for (i in 0 until vertexCount) {
        val angle = angleStep * i - PI / 2
        val endX = center.x + radius * cos(angle).toFloat()
        val endY = center.y + radius * sin(angle).toFloat()
        
        drawLine(
            color = webColor,
            start = center,
            end = Offset(endX, endY),
            strokeWidth = 1.dp.toPx()
        )
    }
}

/**
 * 绘制雷达图数据区域
 */
private fun DrawScope.drawRadarData(
    center: Offset,
    radius: Float,
    data: List<Double>,
    angleStep: Double
) {
    val dataColor = Color(0xFF897457)
    val fillColor = Color(0xFF897457).copy(alpha = 0.44f)
    
    val path = Path()
    
    for (i in data.indices) {
        val angle = angleStep * i - PI / 2
        val dataRadius = radius * (data[i] / 100.0).toFloat()
        val x = center.x + dataRadius * cos(angle).toFloat()
        val y = center.y + dataRadius * sin(angle).toFloat()
        
        if (i == 0) {
            path.moveTo(x, y)
        } else {
            path.lineTo(x, y)
        }
    }
    path.close()
    
    // 填充数据区域
    drawPath(
        path = path,
        color = fillColor
    )
    
    // 绘制数据边框
    drawPath(
        path = path,
        color = dataColor,
        style = Stroke(width = 1.dp.toPx())
    )
}

/**
 * 绘制雷达图标签
 */
private fun DrawScope.drawRadarLabels(
    center: Offset,
    radius: Float,
    labels: List<String>,
    data: List<Double>,
    angleStep: Double
) {
    // 标签绘制需要使用drawIntoCanvas和nativeCanvas
    // 这里简化处理，实际项目中可能需要更复杂的文本绘制逻辑
}

@Preview(showBackground = true)
@Composable
fun ConstitutionRadarHeaderPreview() {
    BamaiTheme {
        ConstitutionRadarHeader(
            constitutionData = ConstitutionData.mock(),
            modifier = Modifier.padding(20.dp)
        )
    }
}

@Preview(showBackground = true)
@Composable
fun ConstitutionRadarHeaderNoDataPreview() {
    BamaiTheme {
        ConstitutionRadarHeader(
            constitutionData = ConstitutionData.mock().copy(
                hasEnoughData = false
            ),
            modifier = Modifier.padding(20.dp)
        )
    }
}
