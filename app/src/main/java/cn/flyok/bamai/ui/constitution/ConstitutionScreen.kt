package cn.flyok.bamai.ui.constitution

import androidx.compose.foundation.background
import androidx.compose.foundation.layout.*
import androidx.compose.material3.*
import androidx.compose.runtime.*
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.text.style.TextAlign
import androidx.compose.ui.tooling.preview.Preview
import androidx.compose.ui.unit.dp
import cn.flyok.bamai.ui.theme.*

/**
 * 体质页面 - 对应iOS端的ConModule
 * 
 * TODO: 后续实现完整的体质测试和结果展示功能
 */
@Composable
fun ConstitutionScreen(
    modifier: Modifier = Modifier
) {
    Column(
        modifier = modifier
            .fillMaxSize()
            .background(BamaiViewBackColor)
            .padding(BamaiDimensions.paddingXLarge),
        horizontalAlignment = Alignment.CenterHorizontally,
        verticalArrangement = Arrangement.Center
    ) {
        Text(
            text = "体质",
            style = MaterialTheme.typography.headlineLarge,
            color = BamaiPrimaryText,
            textAlign = TextAlign.Center
        )
        
        Spacer(modifier = Modifier.height(BamaiDimensions.paddingXLarge))
        
        Text(
            text = "体质测试功能开发中...",
            style = MaterialTheme.typography.bodyLarge,
            color = BamaiSecondaryText,
            textAlign = TextAlign.Center
        )
        
        Spacer(modifier = Modifier.height(BamaiDimensions.paddingXLarge))
        
        Card(
            modifier = Modifier
                .fillMaxWidth()
                .height(200.dp),
            colors = CardDefaults.cardColors(containerColor = BamaiCardBackground),
            shape = androidx.compose.foundation.shape.RoundedCornerShape(BamaiDimensions.cardCornerRadius)
        ) {
            Box(
                modifier = Modifier.fillMaxSize(),
                contentAlignment = Alignment.Center
            ) {
                Text(
                    text = "体质测试卡片占位符",
                    style = MaterialTheme.typography.bodyMedium,
                    color = BamaiSecondaryText
                )
            }
        }
    }
}

@Preview(showBackground = true)
@Composable
fun ConstitutionScreenPreview() {
    BamaiTheme {
        ConstitutionScreen()
    }
}
