package cn.flyok.bamai.ui.constitution

import androidx.compose.foundation.Image
import androidx.compose.foundation.background
import androidx.compose.foundation.border
import androidx.compose.foundation.clickable
import androidx.compose.foundation.interaction.MutableInteractionSource
import androidx.compose.foundation.layout.*
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.material3.*
import androidx.compose.runtime.*
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.clip
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.layout.ContentScale
import androidx.compose.ui.res.painterResource
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.text.style.TextAlign
import androidx.compose.ui.tooling.preview.Preview
import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.sp
import cn.flyok.bamai.R
import cn.flyok.bamai.ui.theme.*

/**
 * 调养方案卡片组件 - 完全复刻iOS端ConNurseTableViewCell
 * 
 * 特点：
 * - 显示今日养动、原则、切记、经穴调养等内容
 * - 非会员显示锁定状态
 */
@Composable
fun NursingPlanCard(
    constitutionData: ConstitutionData,
    modifier: Modifier = Modifier
) {
    Card(
        modifier = modifier.fillMaxWidth(),
        shape = RoundedCornerShape(20.dp),
        colors = CardDefaults.cardColors(containerColor = Color(0xFFFFFDFA)),
        elevation = CardDefaults.cardElevation(defaultElevation = 0.dp)
    ) {
        Box(
            modifier = Modifier
                .fillMaxWidth()
                .padding(5.dp)
        ) {
            Card(
                modifier = Modifier.fillMaxWidth(),
                shape = RoundedCornerShape(20.dp),
                colors = CardDefaults.cardColors(containerColor = Color(0xFFFFFDFA)),
                elevation = CardDefaults.cardElevation(defaultElevation = 0.dp)
            ) {
                Box(
                    modifier = Modifier
                        .fillMaxWidth()
                        .border(
                            width = 1.dp,
                            color = Color(0xFFF0E9E1),
                            shape = RoundedCornerShape(20.dp)
                        )
                        .padding(1.dp)
                ) {
                    Column(
                        modifier = Modifier
                            .fillMaxWidth()
                            .padding(22.dp)
                    ) {
                        // 标题行
                        Row(
                            verticalAlignment = Alignment.CenterVertically
                        ) {
                            Image(
                                painter = painterResource(id = R.drawable.con_tztd),
                                contentDescription = null,
                                modifier = Modifier.size(18.dp)
                            )
                            
                            Spacer(modifier = Modifier.width(5.dp))
                            
                            Text(
                                text = "调养方案",
                                style = MaterialTheme.typography.titleMedium.copy(
                                    fontWeight = FontWeight.Bold,
                                    fontSize = 16.sp
                                ),
                                color = Color(0xFF333333)
                            )
                        }
                        
                        Spacer(modifier = Modifier.height(20.dp))
                        
                        // 内容区域
                        if (constitutionData.isVip && constitutionData.hasEnoughData) {
                            VipNursingContent(constitutionData = constitutionData)
                        } else {
                            LockedNursingContent()
                        }
                    }
                }
            }
        }
    }
}

/**
 * 会员调养内容
 */
@Composable
private fun VipNursingContent(
    constitutionData: ConstitutionData
) {
    Column {
        // 今日养动
        if (constitutionData.todayExercise.isNotEmpty()) {
            NursingSection(
                title = "今日养动",
                items = constitutionData.todayExercise
            )
            
            Spacer(modifier = Modifier.height(15.dp))
        }
        
        // 原则
        if (constitutionData.principles.isNotEmpty()) {
            NursingSection(
                title = "原则",
                items = constitutionData.principles
            )
            
            Spacer(modifier = Modifier.height(15.dp))
        }
        
        // 切记
        if (constitutionData.precautions.isNotEmpty()) {
            NursingSection(
                title = "切记",
                items = constitutionData.precautions
            )
            
            Spacer(modifier = Modifier.height(15.dp))
        }
        
        // 经穴调养
        if (constitutionData.acupointTherapy.isNotEmpty()) {
            Column {
                Text(
                    text = "经穴调养",
                    style = MaterialTheme.typography.titleSmall.copy(
                        fontWeight = FontWeight.Bold,
                        fontSize = 14.sp
                    ),
                    color = Color(0xFF333333)
                )
                
                Spacer(modifier = Modifier.height(8.dp))
                
                Text(
                    text = constitutionData.acupointTherapy,
                    style = MaterialTheme.typography.bodyMedium.copy(
                        fontSize = 14.sp,
                        lineHeight = 19.sp
                    ),
                    color = Color(0xFF333333)
                )
            }
        }
    }
}

/**
 * 调养方案区块
 */
@Composable
private fun NursingSection(
    title: String,
    items: List<String>
) {
    Column {
        Text(
            text = title,
            style = MaterialTheme.typography.titleSmall.copy(
                fontWeight = FontWeight.Bold,
                fontSize = 14.sp
            ),
            color = Color(0xFF333333)
        )
        
        Spacer(modifier = Modifier.height(8.dp))
        
        items.forEach { item ->
            Row(
                modifier = Modifier.fillMaxWidth(),
                verticalAlignment = Alignment.Top
            ) {
                Text(
                    text = "• ",
                    style = MaterialTheme.typography.bodyMedium.copy(
                        fontSize = 14.sp
                    ),
                    color = Color(0xFF333333)
                )
                
                Text(
                    text = item,
                    style = MaterialTheme.typography.bodyMedium.copy(
                        fontSize = 14.sp,
                        lineHeight = 19.sp
                    ),
                    color = Color(0xFF333333),
                    modifier = Modifier.weight(1f)
                )
            }
            
            if (item != items.last()) {
                Spacer(modifier = Modifier.height(4.dp))
            }
        }
    }
}

/**
 * 锁定状态调养内容
 */
@Composable
private fun LockedNursingContent() {
    Box(
        modifier = Modifier
            .fillMaxWidth()
            .height(150.dp)
            .clip(RoundedCornerShape(15.dp))
            .clickable(
                onClick = { /* TODO: 跳转订阅页面 */ },
                indication = null,
                interactionSource = remember { MutableInteractionSource() }
            )
    ) {
        Image(
            painter = painterResource(id = R.drawable.con_nurselocked),
            contentDescription = "解锁调养方案",
            modifier = Modifier.fillMaxSize(),
            contentScale = ContentScale.Crop
        )
    }
}

@Preview(showBackground = true)
@Composable
fun NursingPlanCardVipPreview() {
    BamaiTheme {
        NursingPlanCard(
            constitutionData = ConstitutionData.mock().copy(isVip = true),
            modifier = Modifier.padding(20.dp)
        )
    }
}

@Preview(showBackground = true)
@Composable
fun NursingPlanCardLockedPreview() {
    BamaiTheme {
        NursingPlanCard(
            constitutionData = ConstitutionData.mock().copy(isVip = false),
            modifier = Modifier.padding(20.dp)
        )
    }
}
