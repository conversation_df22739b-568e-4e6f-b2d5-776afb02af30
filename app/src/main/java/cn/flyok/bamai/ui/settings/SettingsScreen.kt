package cn.flyok.bamai.ui.settings

import androidx.compose.foundation.background
import androidx.compose.foundation.layout.*
import androidx.compose.material3.*
import androidx.compose.runtime.*
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.text.style.TextAlign
import androidx.compose.ui.tooling.preview.Preview
import androidx.compose.ui.unit.dp
import cn.flyok.bamai.ui.theme.*

/**
 * 设置页面 - 对应iOS端的SettingModule
 * 
 * TODO: 后续实现完整的设置功能
 */
@Composable
fun SettingsScreen(
    modifier: Modifier = Modifier
) {
    Column(
        modifier = modifier
            .fillMaxSize()
            .background(BamaiViewBackColor)
            .padding(BamaiDimensions.paddingXLarge),
        horizontalAlignment = Alignment.CenterHorizontally,
        verticalArrangement = Arrangement.Center
    ) {
        Text(
            text = "设置",
            style = MaterialTheme.typography.headlineLarge,
            color = BamaiPrimaryText,
            textAlign = TextAlign.Center
        )
        
        Spacer(modifier = Modifier.height(BamaiDimensions.paddingXLarge))
        
        Text(
            text = "设置功能开发中...",
            style = MaterialTheme.typography.bodyLarge,
            color = BamaiSecondaryText,
            textAlign = TextAlign.Center
        )
        
        Spacer(modifier = Modifier.height(BamaiDimensions.paddingXLarge))
        
        Card(
            modifier = Modifier
                .fillMaxWidth()
                .height(200.dp),
            colors = CardDefaults.cardColors(containerColor = BamaiCardBackground),
            shape = androidx.compose.foundation.shape.RoundedCornerShape(BamaiDimensions.cardCornerRadius)
        ) {
            Box(
                modifier = Modifier.fillMaxSize(),
                contentAlignment = Alignment.Center
            ) {
                Text(
                    text = "设置选项占位符",
                    style = MaterialTheme.typography.bodyMedium,
                    color = BamaiSecondaryText
                )
            }
        }
    }
}

@Preview(showBackground = true)
@Composable
fun SettingsScreenPreview() {
    BamaiTheme {
        SettingsScreen()
    }
}
